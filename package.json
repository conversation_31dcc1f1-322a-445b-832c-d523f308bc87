{"name": "survey-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.10", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "expo-image-picker": "~14.3.2", "axios": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-preset-expo": "~9.5.0"}, "private": true}