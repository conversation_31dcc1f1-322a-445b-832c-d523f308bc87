import React from 'react';
import { View, Button, StyleSheet } from 'react-native';

export default function Dashboard({ navigation, route }) {
  const { userId } = route.params;

  return (
    <View style={styles.container}>
      <Button title="Log New Problem" onPress={() => navigation.navigate('LogProblem', { userId })} />
      <Button title="View My Logged Problems" onPress={() => alert('Feature coming soon!')} />
      <Button title="Jurisdiction Search" onPress={() => alert('Feature coming soon!')} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'space-evenly', padding: 16 }
});
