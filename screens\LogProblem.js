import React, { useState } from 'react';
import { View, TextInput, Button, Image, Text, StyleSheet } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import * as ImagePicker from 'expo-image-picker';
import axios from 'axios';

export default function LogProblem({ route }) {
  const { userId } = route.params;
  const [description, setDescription] = useState('');
  const [problemType, setProblemType] = useState('');
  const [location, setLocation] = useState(null);
  const [image, setImage] = useState(null);

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({ allowsEditing: true });
    if (!result.canceled) setImage(result.assets[0].uri);
  };

  const handleMapPress = (e) => {
    setLocation(e.nativeEvent.coordinate);
  };

  const submitProblem = async () => {
    if (!location) return alert("Select a location on the map.");

    const data = {
      type: problemType,
      description,
      coordinates: location,
      createdBy: userId,
      media: image ? [image] : []
    };

    await axios.post('http://<YOUR_SERVER_IP>:5000/api/problems', data);
    alert('Problem Logged!');
  };

  return (
    <View style={styles.container}>
      <Text>Tap on map to select location</Text>
      <MapView style={styles.map} onPress={handleMapPress}>
        {location && <Marker coordinate={location} />}
      </MapView>

      <TextInput placeholder="Problem Type" value={problemType} onChangeText={setProblemType} style={styles.input} />
      <TextInput placeholder="Description" value={description} onChangeText={setDescription} style={styles.input} multiline />

      <Button title="Pick Image" onPress={pickImage} />
      {image && <Image source={{ uri: image }} style={styles.image} />}

      <Button title="Submit Problem" onPress={submitProblem} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
  map: { height: 200, marginBottom: 10 },
  input: { borderWidth: 1, marginBottom: 10, padding: 8 },
  image: { width: 100, height: 100, marginVertical: 10 }
});
