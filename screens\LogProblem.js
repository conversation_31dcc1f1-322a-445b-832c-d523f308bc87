import React, { useState } from 'react';
import { View, TextInput, Button, Image, Text, StyleSheet, Platform, TouchableOpacity } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

export default function LogProblem({ route }) {
  const { userId } = route.params;
  const [description, setDescription] = useState('');
  const [problemType, setProblemType] = useState('');
  const [location, setLocation] = useState(null);
  const [image, setImage] = useState(null);

  const pickImage = async () => {
    try {
      let result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 1,
      });
      if (!result.canceled) setImage(result.assets[0].uri);
    } catch (error) {
      alert('Error picking image: ' + error.message);
    }
  };

  const handleLocationSelect = () => {
    // For demo purposes, set a default location
    const defaultLocation = {
      latitude: 37.78825,
      longitude: -122.4324,
    };
    setLocation(defaultLocation);
    alert('Location selected: ' + JSON.stringify(defaultLocation));
  };

  const submitProblem = async () => {
    if (!location) return alert("Select a location on the map.");
    if (!problemType.trim()) return alert("Please enter a problem type.");
    if (!description.trim()) return alert("Please enter a description.");

    const data = {
      type: problemType,
      description,
      coordinates: location,
      createdBy: userId,
      media: image ? [image] : []
    };

    try {
      // For demo purposes, we'll just show the data instead of making an API call
      console.log('Problem data:', data);
      alert('Problem Logged Successfully!\n\nData: ' + JSON.stringify(data, null, 2));

      // Reset form
      setDescription('');
      setProblemType('');
      setLocation(null);
      setImage(null);
    } catch (error) {
      alert('Error logging problem: ' + error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text>Select location for the problem</Text>
      <TouchableOpacity style={styles.locationButton} onPress={handleLocationSelect}>
        <Text style={styles.locationButtonText}>
          {location ? `Location: ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}` : 'Tap to select location'}
        </Text>
      </TouchableOpacity>

      <TextInput placeholder="Problem Type" value={problemType} onChangeText={setProblemType} style={styles.input} />
      <TextInput placeholder="Description" value={description} onChangeText={setDescription} style={styles.input} multiline />

      <Button title="Pick Image" onPress={pickImage} />
      {image && <Image source={{ uri: image }} style={styles.image} />}

      <Button title="Submit Problem" onPress={submitProblem} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
  locationButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
    alignItems: 'center'
  },
  locationButtonText: {
    color: 'white',
    fontSize: 16
  },
  input: { borderWidth: 1, marginBottom: 10, padding: 8 },
  image: { width: 100, height: 100, marginVertical: 10 }
});
